# 立创·庐山派-K230-CanMV开发板资料与相关扩展板软硬件资料官网全部开源
# 开发板官网：www.lckfb.com
# 技术支持常驻论坛，任何技术问题欢迎随时交流学习
# 立创论坛：www.jlc-bbs.com/lckfb
# 关注bilibili账号：【立创开发板】，掌握我们的最新动态！
# 不靠卖板赚钱，以培养中国工程师为己任

import time
from machine import Pin, FPIOA, WDT

# 创建FPIOA对象，用于初始化引脚功能配置
fpioa = FPIOA()

# 设置引脚功能，将指定的引脚配置为普通GPIO功能
fpioa.set_function(62,FPIOA.GPIO62)
fpioa.set_function(20,FPIOA.GPIO20)
fpioa.set_function(63,FPIOA.GPIO63)
fpioa.set_function(53,FPIOA.GPIO53)

# 实例化Pin62, Pin20, Pin63为输出，分别用于控制红、绿、蓝三个LED灯
LED_R = Pin(62, Pin.OUT, pull=Pin.PULL_NONE, drive=7)  # 红灯
LED_G = Pin(20, Pin.OUT, pull=Pin.PULL_NONE, drive=7)  # 绿灯
LED_B = Pin(63, Pin.OUT, pull=Pin.PULL_NONE, drive=7)  # 蓝灯

# 按键引脚为53，设为下拉输入：未按下为低电平，按下为高电平
button = Pin(53, Pin.IN, Pin.PULL_DOWN)

# 板载RGB灯是共阳结构，高电平=关，低电平=亮
# 初始化时先关闭所有LED
LED_R.high()
LED_G.high()
LED_B.high()

# 定义LED颜色状态 0=红, 1=绿, 2=蓝
color_state = 0

# 初始化硬件看门狗WDT，通道1, 超时时间10秒
wdt = WDT(1, 10)

# 首先喂一次狗，确保开始有充足时间
wdt.feed()

while True:
    # LED轮询：先关全，再亮指定颜色
    LED_R.high()
    LED_G.high()
    LED_B.high()

    if color_state == 0:
        LED_R.low()  # 点亮红灯
    elif color_state == 1:
        LED_G.low()  # 点亮绿灯
    elif color_state == 2:
        LED_B.low()  # 点亮蓝灯

    # 切换到下一个颜色状态
    color_state = (color_state + 1) % 3

    # 检测按钮，如果按下则喂狗
    if button.value() == 1:
        wdt.feed()  # 喂狗，重置WDT计时
        print("wdt feed!")
        # 简单去抖延时
        time.sleep(0.2)

    # 每0.5秒切换一次LED颜色
    time.sleep(0.5)
    # 长时间未喂狗情况：3秒后WDT会超时自动复位