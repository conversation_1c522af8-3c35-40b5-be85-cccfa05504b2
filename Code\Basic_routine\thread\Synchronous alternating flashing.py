# 立创·庐山派-K230-CanMV开发板资料与相关扩展板软硬件资料官网全部开源
# 开发板官网：www.lckfb.com
# 技术支持常驻论坛，任何技术问题欢迎随时交流学习
# 立创论坛：www.jlc-bbs.com/lckfb
# 关注bilibili账号：【立创开发板】，掌握我们的最新动态！
# 不靠卖板赚钱，以培养中国工程师为己任

from machine import Pin, FPIOA
import time
import _thread

# 创建FPIOA对象
fpioa = FPIOA()

# 配置引脚功能
fpioa.set_function(62, FPIOA.GPIO62)
fpioa.set_function(20, FPIOA.GPIO20)
fpioa.set_function(63, FPIOA.GPIO63)

# 实例化LED控制引脚（共阳结构）
LED_R = Pin(62, Pin.OUT, pull=Pin.PULL_NONE, drive=7)  # 红灯
LED_G = Pin(20, Pin.OUT, pull=Pin.PULL_NONE, drive=7)  # 绿灯
LED_B = Pin(63, Pin.OUT, pull=Pin.PULL_NONE, drive=7)  # 蓝灯

# 初始化关闭所有LED
LED_R.high()  # 高电平关闭
LED_G.high()
LED_B.high()

# 创建锁对象控制交替同步
lock = _thread.allocate_lock()
# 共享状态变量 (0:红灯亮，1:蓝灯亮)
state = 0

def red_led_task():
    """红灯控制线程"""
    global state
    while True:
        # 获取锁进行操作
        lock.acquire()
        try:
            if state == 0:
                LED_R.low()   # 点亮红灯
                LED_B.high()  # 关闭蓝灯
                # 更新状态并保持0.5秒
                state = 1
                time.sleep(0.5)
        finally:
            # 确保释放锁
            lock.release()
        # 让出CPU控制权
        time.sleep(0.01)  # 短暂休眠避免忙等待

def blue_led_task():
    """蓝灯控制线程"""
    global state
    while True:
        lock.acquire()
        try:
            if state == 1:
                LED_B.low()   # 点亮蓝灯
                LED_R.high()  # 关闭红灯
                # 更新状态并保持0.5秒
                state = 0
                time.sleep(0.5)
        finally:
            lock.release()
        time.sleep(0.01)

# 启动线程（绿灯保持常灭）
_thread.start_new_thread(red_led_task, ())
_thread.start_new_thread(blue_led_task, ())

# 主线程维持程序运行
while True:
    time.sleep(1)  # 保持主线程不退出