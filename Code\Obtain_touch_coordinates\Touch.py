# 立创·庐山派-K230-CanMV开发板资料与相关扩展板软硬件资料官网全部开源
# 开发板官网：www.lckfb.com
# 技术支持常驻论坛，任何技术问题欢迎随时交流学习
# 立创论坛：www.jlc-bbs.com/lckfb
# 关注bilibili账号：【立创开发板】，掌握我们的最新动态！
# 不靠卖板赚钱，以培养中国工程师为己任

import time
from machine import TOUCH

# 实例化 TOUCH 设备 0
tp = TOUCH(0)

while True:
    # 获取最多 5 个触摸点数据，默认为1.
    p = tp.read(5)

    # 如果返回的 p 为空元组，表示没有触摸
    if p != ():
        print("触摸数据：")
        for idx, point in enumerate(p, start=1):  # 对触摸点进行编号，从 1 开始
            print(f"触摸点 {idx}: X = {point.x}, Y = {point.y}")

    time.sleep(0.01)  # 等待 10 毫秒再读取